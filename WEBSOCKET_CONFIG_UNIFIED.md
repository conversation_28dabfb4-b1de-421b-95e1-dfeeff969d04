# Mario WebSocket 配置统一完成

## 📋 统一结果

已成功将所有 Mario WebSocket 相关的环境变量统一为：

### ✅ **统一的环境变量**
```bash
NEXT_PUBLIC_MARIO_WS_URL=ws://localhost:8080/ws/ag-ui
```

### ❌ **已删除的旧变量**
- `MARIO_WS_ENDPOINT` (环境变量)
- `NEXT_PUBLIC_MARIO_WEBSOCKET_URL`
- `ws://localhost:8080/ws` (旧的 WebSocket 路径)

## 🔧 修改的文件

### 1. 环境配置文件
- **`apps/web/.env.development`**
  - 删除了 `MARIO_WS_ENDPOINT=ws://localhost:8080/ws/ag-ui`
  - 删除了 `NEXT_PUBLIC_MARIO_WEBSOCKET_URL=ws://localhost:8080/ws`
  - 保留了 `NEXT_PUBLIC_MARIO_WS_URL=ws://localhost:8080/ws/ag-ui`

### 2. Hook 文件
- **`apps/web/hooks/use-mario-websocket.ts`**
  - 从 `process.env.NEXT_PUBLIC_MARIO_WEBSOCKET_URL` 改为 `process.env.NEXT_PUBLIC_MARIO_WS_URL`
  - 默认值从 `ws://localhost:8080/ws` 改为 `ws://localhost:8080/ws/ag-ui`

- **`integrations/mario-agent/src/hooks/useAGUIWebSocket.ts`**
  - 已使用 `process.env.NEXT_PUBLIC_MARIO_WS_URL`

- **`integrations/mario-agent/src/hooks/useMarioWebSocket.ts`**
  - 从 `http://localhost:3001` 改为 `ws://localhost:8080/ws/ag-ui`

### 3. API 路由文件
- **`apps/web/app/(backend)/api/integration/mario/[agentId]/route.ts`**
  - 从 `process.env.MARIO_WS_ENDPOINT` 改为 `process.env.NEXT_PUBLIC_MARIO_WS_URL`

### 4. 配置文件
- **`integrations/mario-agent/src/config/mario-config.ts`**
  - 从 `process.env.NEXT_PUBLIC_WS_URL` 改为 `process.env.NEXT_PUBLIC_MARIO_WS_URL`
  - 默认值从 `ws://localhost:3001` 改为 `ws://localhost:8080/ws/ag-ui`

### 5. 测试文件
- **`apps/web/app/test-websocket/page.tsx`**
  - 使用 `process.env.NEXT_PUBLIC_MARIO_WS_URL` 作为默认 URL

### 6. 文档文件
- **`AG_UI_PROTOCOL_INTEGRATION.md`**
  - 更新了环境变量示例
  - 更新了故障排除指南

## 🎯 统一后的配置

### 环境变量
```bash
# apps/web/.env.development
NEXT_PUBLIC_MARIO_WS_URL=ws://localhost:8080/ws/ag-ui
```

### 代码中的使用
```typescript
// 所有文件中统一使用
const wsUrl = process.env.NEXT_PUBLIC_MARIO_WS_URL || 'ws://localhost:8080/ws/ag-ui';
```

## ✅ 验证结果

1. **环境变量统一**: ✅ 所有文件都使用 `NEXT_PUBLIC_MARIO_WS_URL`
2. **WebSocket URL 统一**: ✅ 所有地方都指向 `ws://localhost:8080/ws/ag-ui`
3. **旧配置清理**: ✅ 已删除所有旧的环境变量和配置
4. **文档更新**: ✅ 相关文档已同步更新

## 🚀 使用方法

现在所有 Mario WebSocket 连接都会：

1. **优先使用环境变量**: `process.env.NEXT_PUBLIC_MARIO_WS_URL`
2. **回退到默认值**: `ws://localhost:8080/ws/ag-ui`
3. **使用 AG-UI 协议**: 支持标准化的 AG-UI 事件格式

## 🔍 测试

可以通过以下方式测试配置：

1. **访问测试页面**: http://localhost:3000/test-websocket
2. **检查连接状态**: 确认连接到 `ws://localhost:8080/ws/ag-ui`
3. **发送测试消息**: 验证 AG-UI 协议通信

## 📝 注意事项

- 所有 WebSocket 连接现在都使用 AG-UI 协议
- 环境变量名称已统一，便于维护
- 旧的 `/ws` 端点已被 `/ws/ag-ui` 替代
- 配置更改后需要重启开发服务器
