import { NextRequest, NextResponse } from 'next/server';
import { getIntegration, hasIntegration, createAgent, getIntegrationList } from '@workspace/agent-registry/server';
import { CopilotRuntime, copilotRuntimeNextJSAppRouterEndpoint, OpenAIAdapter } from '@copilotkit/runtime';
import OpenAI from 'openai';
import WebSocket from 'ws';
import { generateMessageId } from '@workspace/shared';

/**
 * Mario WebSocket API 端点配置
 * 实际的Mario服务运行在WebSocket上
 * 修改为本地Python LangGraph服务
 */
const MARIO_WS_ENDPOINT = process.env.NEXT_PUBLIC_MARIO_WS_URL || 'ws://localhost:8080/ws/ag-ui';

/**
 * 创建OpenAI实例和服务适配器 for Mario
 */
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '1914304559263223873',
  baseURL: process.env.OPENAI_BASE_URL || 'https://aigc.sankuai.com/v1/openai/native/'
});

/**
 * 创建CopilotKit的OpenAI适配器 for <PERSON>
 */
const serviceAdapter = new OpenAIAdapter({
  openai,
  model: 'gpt-4o-2024-11-20'
});

/**
 * WebSocket连接管理器
 * 用于管理与Mario服务的WebSocket连接
 */
class MarioWebSocketManager {
  private static connections = new Map<string, WebSocket>();

  /**
   * 获取或创建WebSocket连接
   * @param agentId - Agent ID
   * @param sessionId - 会话ID
   * @param retries - 重试次数
   */
  static async getConnection(agentId: string, sessionId?: string, retries = 3): Promise<WebSocket> {
    const connectionKey = `${agentId}-${sessionId || 'default'}`;

    if (this.connections.has(connectionKey)) {
      const existingWs = this.connections.get(connectionKey)!;
      if (existingWs.readyState === WebSocket.OPEN) {
        return existingWs;
      } else {
        this.connections.delete(connectionKey);
      }
    }

    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        console.log(`🔌 WebSocket连接尝试 ${attempt}/${retries} - Connecting to: ${MARIO_WS_ENDPOINT}`);
        console.log(`📋 Connection params: agentId=${agentId}, sessionId=${sessionId}`);

        const ws = await this.createConnection(connectionKey);
        return ws;
      } catch (error) {
        console.error(`❌ WebSocket连接尝试 ${attempt}/${retries} 失败:`, error);
        if (attempt === retries) {
          throw error;
        }
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
      }
    }

    throw new Error('WebSocket连接重试失败');
  }

  /**
   * 创建WebSocket连接
   * @param connectionKey - 连接键
   */
  private static createConnection(connectionKey: string): Promise<WebSocket> {
    return new Promise((resolve, reject) => {
      try {
        const ws = new WebSocket(MARIO_WS_ENDPOINT);

        const timeout = setTimeout(() => {
          ws.close();
          reject(new Error('WebSocket连接超时'));
        }, 10000); // 10秒连接超时

        ws.on('open', () => {
          clearTimeout(timeout);
          console.log('✅ WebSocket connection established');
          this.connections.set(connectionKey, ws);
          resolve(ws);
        });

        ws.on('error', (error) => {
          clearTimeout(timeout);
          console.error('❌ WebSocket connection error:', error);
          reject(error);
        });

        ws.on('close', () => {
          console.log('🔌 WebSocket connection closed');
          this.connections.delete(connectionKey);
        });
      } catch (error) {
        console.error('❌ Failed to create WebSocket connection:', error);
        reject(error);
      }
    });
  }

  /**
   * 发送消息到AG-UI WebSocket
   * @param ws - WebSocket连接
   * @param message - 要发送的消息
   */
  static async sendMessage(ws: WebSocket, message: any): Promise<string> {
    return new Promise((resolve, reject) => {
      if (ws.readyState !== WebSocket.OPEN) {
        reject(new Error('WebSocket connection is not open'));
        return;
      }

      // 验证消息内容
      const messageContent = message.message || message.content || '';
      if (!messageContent.trim()) {
        console.error('❌ 消息内容为空或无效:', { message });
        reject(new Error('消息内容不能为空'));
        return;
      }

      let responseContent = '';
      let isCompleted = false;

      // 监听AG-UI事件
      const messageHandler = (data: WebSocket.Data) => {
        try {
          const event = JSON.parse(data.toString());
          console.log('Received AG-UI event:', event.type, event);

          switch (event.type) {
            case 'run_started':
              console.log('AG-UI run started');
              break;

            case 'text_message_delta':
              if (event.delta) {
                responseContent += event.delta;
              }
              break;

            case 'text_message_end':
              console.log('AG-UI text message completed');
              break;

            case 'run_finished':
              console.log('AG-UI run finished');
              isCompleted = true;
              ws.off('message', messageHandler);
              resolve(responseContent || 'Response completed');
              break;

            case 'error':
              console.error('AG-UI error:', event.error);
              console.error('AG-UI error details:', event);
              ws.off('message', messageHandler);
              // 提供更详细的错误信息
              const errorMsg = event.error || 'AG-UI processing error';
              reject(new Error(`AG-UI服务错误: ${errorMsg}`));
              break;

            case 'interrupt':
              console.log('AG-UI interrupted:', event.error);
              ws.off('message', messageHandler);
              resolve(responseContent || 'Response interrupted');
              break;

            default:
              console.log('Unhandled AG-UI event:', event.type, event);
          }
        } catch (error) {
          console.error('Failed to parse AG-UI event:', error);
          console.error('Raw event data:', data.toString());
        }
      };

      ws.on('message', messageHandler);

      // 构建AG-UI格式的请求，确保消息内容有效
      const aguiRequest = {
        type: 'run',
        messages: [
          {
            role: 'user',
            content: messageContent
          }
        ],
        thread_id: message.sessionId || `thread-${Date.now()}`,
        run_id: `run-${Date.now()}`,
        tools: [],
        state: {
          agentId: message.agentId,
          agentConfig: message.agentConfig
        }
      };

      console.log('📤 发送到AG-UI的请求:', JSON.stringify(aguiRequest, null, 2));

      // 发送AG-UI格式的消息
      ws.send(JSON.stringify(aguiRequest), (error) => {
        if (error) {
          console.error('❌ 发送消息到AG-UI失败:', error);
          ws.off('message', messageHandler);
          reject(error);
        }
      });

      // 设置超时
      setTimeout(() => {
        if (!isCompleted) {
          ws.off('message', messageHandler);
          reject(new Error('AG-UI WebSocket response timeout'));
        }
      }, 60000); // 60秒超时，AG-UI可能需要更长时间
    });
  }

  /**
   * 关闭WebSocket连接
   * @param agentId - Agent ID
   * @param sessionId - 会话ID
   */
  static closeConnection(agentId: string, sessionId?: string): void {
    const connectionKey = `${agentId}-${sessionId || 'default'}`;
    const ws = this.connections.get(connectionKey);
    if (ws) {
      ws.close();
      this.connections.delete(connectionKey);
    }
  }
}

/**
 * Mario CopilotKit API 处理器
 * 支持 CopilotKit 协议的 Mario Agent 端点
 */
export const POST = async (request: NextRequest) => {
  try {
    console.log('=== Mario CopilotKit API Called ===');
    console.log('Request URL:', request.url);
    console.log('Request method:', request.method);

    // 从 URL 路径中提取 agentId，去除查询参数
    const urlParts = request.url.split('/');
    const lastPart = urlParts.pop() || '';
    const agentId = lastPart.split('?')[0]; // 去除查询参数
    console.log('🔍 Extracted agentId:', agentId);

    if (!agentId) {
      console.error('❌ Agent ID not provided in URL');
      return new Response(
        JSON.stringify({
          error: 'Agent ID not provided',
          details: 'Agent ID is required in the URL path'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // 验证 agent 配置是否存在
    console.log('🔍 Checking if agent configuration exists...');
    const agentInfo = await getIntegration(agentId);
    if (!agentInfo) {
      console.error(`❌ Agent configuration for ${agentId} not found`);
      const availableAgents = await getIntegrationList();
      console.log(
        'Available agents:',
        availableAgents.map((a) => a.id)
      );
      return new Response(
        JSON.stringify({
          error: 'Agent configuration not found',
          details: `Agent ${agentId} is not configured`,
          requestedAgent: agentId,
          availableAgents: availableAgents.map((a) => a.id)
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('✅ Agent configuration found:', agentInfo.name);
    console.log(
      '📋 All registered agents:',
      (await getIntegrationList()).map((a) => ({ id: a.id, name: a.name }))
    );

    // 检查 agent 是否存在于注册中心
    console.log('🔍 Checking if agent exists in registry...');
    if (!(await hasIntegration(agentId))) {
      console.error(`❌ Agent ${agentId} not found in registry`);
      return new Response(
        JSON.stringify({
          error: 'Agent not found in registry',
          details: `Agent ${agentId} is not registered in the agent registry`,
          requestedAgent: agentId,
          availableAgents: (await getIntegrationList()).map((integration) => integration.id)
        }),
        {
          status: 404,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    console.log('✅ Agent exists in registry');

    // 从注册中心获取指定的 Mastra agent 实例
    console.log('🚀 Getting Mastra agent configuration...');
    const mastraAgent = await createAgent(agentId);
    console.log('✅ Retrieved Mastra agent configuration');
    console.log('📝 Agent info:', {
      name: mastraAgent.name,
      hasInstructions: !!mastraAgent.instructions,
      instructionsLength: mastraAgent.instructions?.length || 0
    });

    // 创建 CopilotRuntime 实例，配置 Mario 特定的远程端点
    const runtime = new CopilotRuntime({
      remoteEndpoints: [
        {
          url: MARIO_WS_ENDPOINT,
          // 可以添加其他配置
        }
      ]
    });

    console.log('🚀 Created CopilotRuntime with Mario WebSocket endpoint:', MARIO_WS_ENDPOINT);

    // 使用 CopilotKit 的标准端点处理器
    const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
      runtime,
      serviceAdapter,
      endpoint: `/api/integration/mario/${agentId}`,
    });

    console.log('✅ Mario CopilotKit endpoint configured successfully');
    return handleRequest(request);
  } catch (error) {
    console.error('💥 Mario CopilotKit API error:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return new Response(
      JSON.stringify({
        error: 'Mario CopilotKit API error',
        details: errorMessage,
        stack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
        agentId: request.url.split('/').pop(),
        mode: 'mario-copilotkit'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
};

/**
 * 处理 GET 请求 - 用于健康检查和状态查询
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const agentId = pathParts[pathParts.length - 1];
    const sessionId = url.searchParams.get('sessionId');

    console.log('🔍 Mario API Health Check - Agent:', agentId, 'Session:', sessionId);

    // 验证 agent 是否存在
    if (agentId && !(await hasIntegration(agentId))) {
      return NextResponse.json(
        {
          error: 'Agent not found',
          agentId
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      status: 'healthy',
      mode: 'mario',
      agentId,
      sessionId,
      timestamp: new Date().toISOString(),
      availableAgents: (await getIntegrationList()).map((a) => ({ id: a.id, name: a.name }))
    });
  } catch (error) {
    console.error('💥 Mario API health check error:', error);
    return NextResponse.json(
      {
        status: 'error',
        mode: 'mario',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
