/**
 * Agent 配置自动加载器
 * 负责动态加载和注册所有 Agent 的配置
 */

import { registerAgentConfig, hasAgentConfig, AgentIntegrationType } from '@/hooks/use-agent-mode';

/**
 * 手动注册已知的 Agent 配置
 * 这个函数在构建时安全，不依赖动态导入
 */
export function registerKnownAgentConfigs() {
  console.log('🔧 Registering known agent configurations...');

  // 注册 Haiku Agent 配置
  registerAgentConfig('haiku', {
    integrationType: AgentIntegrationType.COPILOTKIT,
    apiEndpoint: '/api/integration/copilotkit',
    requiresSpecialHandling: false,
    displayNameTemplate: '{name}',
    customConfig: {
      supportedFeatures: ['poetry', 'creative-writing', 'chinese-poetry'],
      language: 'zh-CN',
      model: 'gpt-4o'
    }
  });
  console.log('✅ Registered config for haiku');

  // 注册 Mario Agent 配置
  registerAgentConfig('mario', {
    integrationType: AgentIntegrationType.MARIO,
    apiEndpoint: '/api/integration/mario',
    requiresSpecialHandling: true,
    displayNameTemplate: '<PERSON> {name}',
    customConfig: {
      websocketEndpoint: 'ws://localhost:8080/ws/ag-ui',
      supportedFeatures: ['canvas', 'testing', 'visualization', 'automation', 'docker'],
      dockerSupport: true,
      terminalSupport: true,
      codeGeneration: true,
      testFramework: 'TestNG',
      languages: ['Java', 'Python'],
      protocols: ['HTTP', 'Thrift', 'gRPC']
    }
  });
  console.log('✅ Registered config for mario');

  console.log('🎉 Known agent configuration registration completed');
}

/**
 * 自动注册所有 Agent 配置（运行时动态加载）
 * 这个函数应该在客户端运行时调用
 */
export async function loadAgentConfigs() {
  console.log('🔧 Loading agent configurations...');

  try {
    // 首先注册已知配置
    registerKnownAgentConfigs();

    // 然后尝试动态加载更多配置（仅在客户端运行时）
    if (typeof window !== 'undefined') {
      // 延迟执行动态导入，避免构建时分析
      setTimeout(() => {
        loadDynamicAgentConfigs().catch(error => {
          console.warn('Failed to load dynamic agent configs:', error);
        });
      }, 100);
    }

    console.log('🎉 Agent configuration loading completed');
  } catch (error) {
    console.error('💥 Failed to load agent configurations:', error);
  }
}

/**
 * 动态加载 Agent 配置（仅运行时）
 * 使用字符串拼接避免构建时分析
 */
async function loadDynamicAgentConfigs() {
  // 只在浏览器环境中执行动态导入
  if (typeof window === 'undefined') {
    console.log('⚠️ Skipping dynamic imports in server environment');
    return;
  }

  console.log('🔄 Attempting to load dynamic agent configurations...');

  // 使用字符串拼接避免构建时分析动态导入
  const workspacePrefix = '@workspace/';

  // 动态导入 Haiku Agent 配置
  try {
    const modulePath = workspacePrefix + 'haiku-agent';
    const module = await import(/* webpackIgnore: true */ modulePath);
    const HaikuIntegration = module.HaikuIntegration || module.default;
    if (HaikuIntegration?.integrationConfig) {
      registerAgentConfig(HaikuIntegration.id, HaikuIntegration.integrationConfig);
      console.log(`✅ Dynamically registered config for ${HaikuIntegration.id}`);
    }
  } catch (error) {
    console.warn('⚠️ Failed to dynamically load Haiku Agent config:', error);
  }

  // 动态导入 Mario Agent 配置
  try {
    const modulePath = workspacePrefix + 'mario-agent';
    const module = await import(/* webpackIgnore: true */ modulePath);
    const MarioIntegration = module.MarioIntegration || module.default;
    if (MarioIntegration?.integrationConfig) {
      registerAgentConfig(MarioIntegration.id, MarioIntegration.integrationConfig);
      console.log(`✅ Dynamically registered config for ${MarioIntegration.id}`);
    }
  } catch (error) {
    console.warn('⚠️ Failed to dynamically load Mario Agent config:', error);
  }
}

/**
 * 从 Agent Registry 自动发现和注册配置
 * 这是一个更高级的方法，可以自动发现所有可用的 Agent
 */
export async function autoDiscoverAgentConfigs() {
  console.log('🔍 Auto-discovering agent configurations...');
  
  try {
    // 获取所有已注册的集成
    const response = await fetch('/api/agents?fast=true');
    if (!response.ok) {
      throw new Error('Failed to fetch agents');
    }
    
    const data = await response.json();
    const agents = data.agents || [];
    
    // 为每个 Agent 尝试加载配置
    for (const agent of agents) {
      try {
        await loadAgentConfigById(agent.id);
      } catch (error) {
        console.warn(`⚠️ Failed to load config for agent ${agent.id}:`, error);
      }
    }
    
    console.log('🎉 Auto-discovery completed');
  } catch (error) {
    console.error('💥 Auto-discovery failed:', error);
    // 如果自动发现失败，回退到手动加载
    await loadAgentConfigs();
  }
}

/**
 * 根据 Agent ID 动态加载配置
 */
async function loadAgentConfigById(agentId: string) {
  // 如果已经注册过，跳过
  if (hasAgentConfig(agentId)) {
    return;
  }

  // 只在浏览器环境中执行动态导入
  if (typeof window === 'undefined') {
    console.log('⚠️ Skipping dynamic config loading in server environment');
    return;
  }

  const configLoaders: Record<string, () => Promise<any>> = {
    'haiku': () => import('@workspace/haiku-agent').then(m => m.HaikuIntegration || m.default),
    'mario': () => import('@workspace/mario-agent').then(m => m.MarioIntegration || m.default),
    // 可以继续添加更多 Agent
  };

  const loader = configLoaders[agentId];
  if (loader) {
    try {
      const integration = await loader();
      if (integration.integrationConfig) {
        registerAgentConfig(integration.id, integration.integrationConfig);
        console.log(`✅ Auto-registered config for ${integration.id}`);
      }
    } catch (error) {
      console.warn(`⚠️ Failed to load config for ${agentId}:`, error);
    }
  }
}

/**
 * 在客户端初始化 Agent 配置
 * 这个函数应该在客户端应用启动时调用
 */
export function initializeClientAgentConfigs() {
  if (typeof window !== 'undefined') {
    // 客户端环境
    autoDiscoverAgentConfigs().catch(error => {
      console.error('Failed to initialize client agent configs:', error);
    });
  }
}

/**
 * 在服务端初始化 Agent 配置
 * 这个函数应该在服务端应用启动时调用
 */
export function initializeServerAgentConfigs() {
  if (typeof window === 'undefined') {
    // 服务端环境
    loadAgentConfigs().catch(error => {
      console.error('Failed to initialize server agent configs:', error);
    });
  }
}
