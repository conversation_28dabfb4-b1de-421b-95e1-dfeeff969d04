import { generateText } from 'ai';
import type { AgentTool } from '@workspace/shared';
import { customOpenAI } from '@workspace/shared';
import type { GenerateParams, GenerateResult } from '../types.js';

export class ContentGeneratorTool implements AgentTool {
  id = 'content-generator';
  name = 'Content Generator';
  description = 'Generate haiku content based on a given topic';

  async execute(params: GenerateParams): Promise<GenerateResult> {
    const { topic, style = 'traditional', outputFormat = 'both', includeExtras = true } = params;

    console.log('Generating haiku for topic:', topic);

    // 不使用额外内容
    const selectedExtras: string[] = [];

    try {
      // 使用真实的AI模型生成诗歌
      const { text } = await generateText({
        model: customOpenAI('gpt-4o-2024-11-20'),
        prompt: `
          你是一个专业的诗歌生成助手。请根据主题"${topic}"创作一首诗歌。
          要求：
          1. 严格遵循诗歌的5-7-5音节结构
          2. 捕捉主题的精髓和情感
          3. 使用优美的意象和语言
          4. 风格：${style}

          请返回JSON格式：
          {
            "chinese": "中文诗歌（三行，5-7-5音节，用\\n分隔）",
            "english": "英文诗歌（三行，5-7-5音节，用\\n分隔）"
          }
        `
      });

      // 解析AI生成的内容
      let parsedContent;
      try {
        parsedContent = JSON.parse(text);
      } catch (parseError) {
        // 如果解析失败，使用备用格式
        console.warn('Failed to parse AI response, using fallback');
        parsedContent = {
          chinese: text.split('\n').slice(0, 3).join('\n'),
          english: 'Spring wind blows\nCherry blossoms falling\nIn peaceful silence'
        };
      }

      return {
        primary: parsedContent.chinese || '春风轻拂\n樱花飘落满地\n静谧无声',
        secondary: parsedContent.english || 'Spring wind blows\nCherry blossoms falling\nIn peaceful silence',
        extras: selectedExtras,
        metadata: {
          style,
          outputFormat,
          generatedAt: new Date().toISOString(),
          aiGenerated: true
        }
      };
    } catch (error) {
      console.error('AI generation failed, using fallback:', error);

      // 如果AI生成失败，回退到模拟数据
      const mockContent = {
        traditional: {
          primary: '春风轻拂\n樱花飘落满地\n静谧无声',
          secondary: 'Spring wind blows\nCherry blossoms falling\nIn peaceful silence'
        },
        modern: {
          primary: '城市夜晚\n霓虹灯光闪耀\n孤独的心',
          secondary: 'City at night\nNeon lights shining bright\nA lonely heart'
        },
        experimental: {
          primary: '超越时空\n数字波浪流淌\n向着未来',
          secondary: 'Beyond spacetime\nDigital waves flowing\nInto the future'
        }
      };

      const selectedStyle = mockContent[style as keyof typeof mockContent] || mockContent.traditional;

      return {
        primary: selectedStyle.primary,
        secondary: selectedStyle.secondary,
        extras: selectedExtras,
        metadata: {
          style,
          outputFormat,
          generatedAt: new Date().toISOString(),
          aiGenerated: false,
          fallback: true
        }
      };
    }
  }
}

// 导出工具实例
export const contentGeneratorTool = new ContentGeneratorTool();
