import { generateText } from 'ai';
import type { AgentTool } from '@workspace/shared';
import { customOpenAI } from '@workspace/shared';

export class TopicExtractorTool implements AgentTool {
  id = 'topic-extractor';
  name = 'Topic Extractor';
  description = "Extract the main topic from the user's message";

  /**
   * 执行主题提取
   * 添加更严格的输入验证和错误处理
   */
  async execute(input: string): Promise<string> {
    // 验证输入不为空
    if (!input || input.trim().length === 0) {
      console.warn('TopicExtractorTool: Empty input provided, returning default topic');
      return 'general';
    }

    try {
      const result = await generateText({
        model: customOpenAI('gpt-4o-2024-11-20'),
        prompt: `Extract the main topic from this user input. Return only the topic, nothing else.\n\nUser input: ${input.trim()}`,
        maxTokens: 50
      });

      // 验证 AI 返回结果
      const extractedTopic = result.text?.trim();
      if (!extractedTopic) {
        console.warn('TopicExtractorTool: AI returned empty result, using fallback');
        return input.trim();
      }

      return extractedTopic;
    } catch (error) {
      console.error('TopicExtractorTool: AI topic extraction failed, using fallback:', error);
      // 返回清理后的原始输入作为备用
      return input.trim() || 'general';
    }
  }
}

// 导出工具实例
export const topicExtractorTool = new TopicExtractorTool();
