#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebSocket修复测试脚本
用于测试AG-UI WebSocket连接和judge_node的修复效果
"""

import asyncio
import json
import websockets
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# WebSocket服务器地址
WS_URL = "ws://localhost:8080/ws/ag-ui"

async def test_websocket_connection():
    """测试WebSocket连接"""
    try:
        logger.info(f"尝试连接到 {WS_URL}")
        async with websockets.connect(WS_URL) as websocket:
            logger.info("WebSocket连接成功建立")
            
            # 测试消息1：正常的用例生成请求
            test_message_1 = {
                "type": "run",
                "messages": [
                    {
                        "role": "user",
                        "content": "帮我生成Mario用例, EC用例Id：2182608"
                    }
                ],
                "thread_id": f"test-thread-{datetime.now().timestamp()}",
                "run_id": f"test-run-{datetime.now().timestamp()}",
                "tools": [],
                "state": {
                    "agentId": "mario",
                    "agentType": "mario"
                }
            }
            
            logger.info("发送测试消息1（包含EC ID）...")
            await websocket.send(json.dumps(test_message_1))
            
            # 接收响应
            response_count = 0
            while response_count < 10:  # 最多接收10个响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    logger.info(f"收到响应 {response_count + 1}: {response_data.get('type', 'unknown')}")
                    
                    if response_data.get('type') == 'error':
                        logger.error(f"收到错误响应: {response_data.get('error')}")
                        break
                    elif response_data.get('type') == 'run_finished':
                        logger.info("工作流完成")
                        break
                        
                    response_count += 1
                except asyncio.TimeoutError:
                    logger.info("响应超时，继续等待...")
                    break
            
            # 测试消息2：不包含EC ID的请求
            test_message_2 = {
                "type": "run",
                "messages": [
                    {
                        "role": "user",
                        "content": "你好，请帮我生成一个测试用例"
                    }
                ],
                "thread_id": f"test-thread-{datetime.now().timestamp()}",
                "run_id": f"test-run-{datetime.now().timestamp()}",
                "tools": [],
                "state": {
                    "agentId": "mario",
                    "agentType": "mario"
                }
            }
            
            logger.info("发送测试消息2（不包含EC ID）...")
            await websocket.send(json.dumps(test_message_2))
            
            # 接收响应
            response_count = 0
            while response_count < 10:
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    response_data = json.loads(response)
                    logger.info(f"收到响应 {response_count + 1}: {response_data.get('type', 'unknown')}")
                    
                    if response_data.get('type') == 'error':
                        logger.error(f"收到错误响应: {response_data.get('error')}")
                        break
                    elif response_data.get('type') == 'run_finished':
                        logger.info("工作流完成")
                        break
                        
                    response_count += 1
                except asyncio.TimeoutError:
                    logger.info("响应超时，继续等待...")
                    break
            
            # 测试消息3：空消息（应该返回错误）
            test_message_3 = {
                "type": "run",
                "messages": [
                    {
                        "role": "user",
                        "content": ""
                    }
                ],
                "thread_id": f"test-thread-{datetime.now().timestamp()}",
                "run_id": f"test-run-{datetime.now().timestamp()}",
                "tools": [],
                "state": {}
            }
            
            logger.info("发送测试消息3（空内容）...")
            await websocket.send(json.dumps(test_message_3))
            
            # 接收响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                logger.info(f"空消息响应: {response_data}")
                
                if response_data.get('type') == 'error':
                    logger.info("✅ 空消息正确返回错误")
                else:
                    logger.warning("⚠️ 空消息没有返回预期的错误")
            except asyncio.TimeoutError:
                logger.warning("空消息测试超时")
            
            logger.info("WebSocket测试完成")
            
    except Exception as e:
        logger.error(f"WebSocket连接失败: {e}")
        return False
    
    return True

async def test_llm_model():
    """测试LLM模型是否正常工作"""
    try:
        logger.info("测试LLM模型...")
        from apps.agents.model import get_llm_coder_type
        
        llm = get_llm_coder_type()
        test_messages = [{"role": "user", "content": "请回复：测试成功"}]
        
        response = llm.invoke(test_messages)
        if response and hasattr(response, 'content') and response.content:
            logger.info(f"✅ LLM模型测试成功: {response.content[:100]}...")
            return True
        else:
            logger.error("❌ LLM模型测试失败：响应为空")
            return False
            
    except Exception as e:
        logger.error(f"❌ LLM模型测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    logger.info("开始WebSocket修复验证测试...")
    
    # 测试LLM模型
    llm_ok = await test_llm_model()
    
    # 测试WebSocket连接
    ws_ok = await test_websocket_connection()
    
    # 总结测试结果
    logger.info("=" * 50)
    logger.info("测试结果总结:")
    logger.info(f"LLM模型: {'✅ 正常' if llm_ok else '❌ 异常'}")
    logger.info(f"WebSocket: {'✅ 正常' if ws_ok else '❌ 异常'}")
    
    if llm_ok and ws_ok:
        logger.info("🎉 所有测试通过！修复成功！")
    else:
        logger.error("❌ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    asyncio.run(main())
